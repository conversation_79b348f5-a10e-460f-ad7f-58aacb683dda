<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>is Cortex - Web3 AI Assistant</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🧠 Moralis Cortex</h1>
            <p>Ask questions about blockchain data in natural language</p>
        </header>

        <main>
            <div class="chat-container">
                <div class="query-section">
                    <form id="queryForm">
                        <div class="input-group">
                            <textarea 
                                id="queryInput" 
                                placeholder="Ask anything about blockchain data... e.g., 'What's the current price of PEPE?' or 'Show me NFTs owned by vitalik.eth'"
                                rows="3"
                                required
                            ></textarea>
                            <button type="submit" id="submitBtn">
                                <span id="submitText">Ask Cortex</span>
                                <span id="loadingSpinner" class="spinner hidden">⏳</span>
                            </button>
                        </div>
                    </form>
                </div>

                <div class="response-section">
                    <div id="responseContainer" class="hidden">
                        <h3>Response:</h3>
                        <div id="responseContent"></div>
                        <button id="copyBtn" class="copy-btn">📋 Copy Response</button>
                    </div>
                    
                    <div id="errorContainer" class="error-message hidden">
                        <h3>Error:</h3>
                        <div id="errorContent"></div>
                    </div>
                </div>
            </div>

            <div class="examples-section">
                <h3>💡 Example Queries:</h3>
                <div class="examples-grid">
                    <button class="example-btn" data-query="What's the current price of PEPE and Ethereum?">
                        💰 Token Prices
                    </button>
                    <button class="example-btn" data-query="Show me the NFTs owned by vitalik.eth">
                        🖼️ NFT Ownership
                    </button>
                    <button class="example-btn" data-query="What tokens does wallet ****************************************** hold?">
                        👛 Wallet Tokens
                    </button>
                    <button class="example-btn" data-query="What is the current net worth of wallet ******************************************?">
                        💎 Wallet Value
                    </button>
                    <button class="example-btn" data-query="Analyze the holder distribution of SHIB token">
                        📊 Token Analysis
                    </button>
                    <button class="example-btn" data-query="Show me PEPE's price trend over the past 30 days">
                        📈 Price Trends
                    </button>
                </div>
            </div>
        </main>

        <footer>
            <p>Powered by <a href="https://moralis.com/cortex/" target="_blank">Moralis Cortex API</a></p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
