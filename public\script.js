class CortexChat {
    constructor() {
        this.chatId = null; // For conversation continuity
        this.initializeElements();
        this.attachEventListeners();
    }

    initializeElements() {
        this.queryForm = document.getElementById('queryForm');
        this.queryInput = document.getElementById('queryInput');
        this.submitBtn = document.getElementById('submitBtn');
        this.submitText = document.getElementById('submitText');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.responseContainer = document.getElementById('responseContainer');
        this.responseContent = document.getElementById('responseContent');
        this.errorContainer = document.getElementById('errorContainer');
        this.errorContent = document.getElementById('errorContent');
        this.copyBtn = document.getElementById('copyBtn');
        this.exampleBtns = document.querySelectorAll('.example-btn');
    }

    attachEventListeners() {
        // Form submission
        this.queryForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleQuery();
        });

        // Copy button
        this.copyBtn.addEventListener('click', () => {
            this.copyResponse();
        });

        // Example buttons
        this.exampleBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const query = btn.getAttribute('data-query');
                this.queryInput.value = query;
                this.queryInput.focus();
            });
        });

        // Auto-resize textarea
        this.queryInput.addEventListener('input', () => {
            this.autoResizeTextarea();
        });
    }

    autoResizeTextarea() {
        this.queryInput.style.height = 'auto';
        this.queryInput.style.height = Math.min(this.queryInput.scrollHeight, 200) + 'px';
    }

    async handleQuery() {
        const query = this.queryInput.value.trim();
        
        if (!query) {
            this.showError('Please enter a query');
            return;
        }

        this.setLoading(true);
        this.hideMessages();

        try {
            const response = await this.sendQuery(query);
            this.showResponse(response);
        } catch (error) {
            this.showError(error.message);
        } finally {
            this.setLoading(false);
        }
    }

    async sendQuery(query) {
        const requestBody = {
            query: query
        };

        // Include chatId for conversation continuity
        if (this.chatId) {
            requestBody.chatId = this.chatId;
        }

        const response = await fetch('/api/cortex/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        
        // Store chatId for conversation continuity
        if (data.chatId) {
            this.chatId = data.chatId;
        }

        return data;
    }

    showResponse(data) {
        this.hideMessages();
        
        // Format the response for display
        let displayContent = '';
        
        if (data.summary) {
            displayContent += `📋 Summary:\n${data.summary}\n\n`;
        }
        
        if (data.data) {
            displayContent += `📊 Data:\n${JSON.stringify(data.data, null, 2)}\n\n`;
        }
        
        if (data.response) {
            displayContent += `🤖 Response:\n${data.response}\n\n`;
        }
        
        // If none of the expected fields are present, show the raw response
        if (!displayContent.trim()) {
            displayContent = JSON.stringify(data, null, 2);
        }

        this.responseContent.textContent = displayContent.trim();
        this.responseContainer.classList.remove('hidden');
        
        // Scroll to response
        this.responseContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    showError(message) {
        this.hideMessages();
        this.errorContent.textContent = message;
        this.errorContainer.classList.remove('hidden');
        
        // Scroll to error
        this.errorContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    hideMessages() {
        this.responseContainer.classList.add('hidden');
        this.errorContainer.classList.add('hidden');
    }

    setLoading(isLoading) {
        if (isLoading) {
            this.submitBtn.disabled = true;
            this.submitText.classList.add('hidden');
            this.loadingSpinner.classList.remove('hidden');
            this.queryInput.disabled = true;
        } else {
            this.submitBtn.disabled = false;
            this.submitText.classList.remove('hidden');
            this.loadingSpinner.classList.add('hidden');
            this.queryInput.disabled = false;
        }
    }

    async copyResponse() {
        try {
            await navigator.clipboard.writeText(this.responseContent.textContent);
            
            // Visual feedback
            const originalText = this.copyBtn.textContent;
            this.copyBtn.textContent = '✅ Copied!';
            this.copyBtn.style.background = '#28a745';
            
            setTimeout(() => {
                this.copyBtn.textContent = originalText;
                this.copyBtn.style.background = '#6c757d';
            }, 2000);
        } catch (error) {
            console.error('Failed to copy:', error);
            
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = this.responseContent.textContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            
            this.copyBtn.textContent = '✅ Copied!';
            setTimeout(() => {
                this.copyBtn.textContent = '📋 Copy Response';
            }, 2000);
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CortexChat();
    
    // Check if server is healthy
    fetch('/api/health')
        .then(response => response.json())
        .then(data => {
            if (!data.apiKeyConfigured) {
                console.warn('⚠️ Moralis API key not configured');
            }
        })
        .catch(error => {
            console.error('Health check failed:', error);
        });
});
