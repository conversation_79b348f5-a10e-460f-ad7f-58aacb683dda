* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

main {
    flex: 1;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.chat-container {
    margin-bottom: 40px;
}

.query-section {
    margin-bottom: 30px;
}

.input-group {
    display: flex;
    gap: 15px;
    align-items: flex-end;
}

#queryInput {
    flex: 1;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    transition: border-color 0.3s ease;
}

#queryInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#submitBtn {
    padding: 15px 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    min-width: 140px;
    height: fit-content;
}

#submitBtn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

#submitBtn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.spinner {
    display: inline-block;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.hidden {
    display: none !important;
}

.response-section {
    margin-top: 30px;
}

#responseContainer {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #28a745;
}

#responseContainer h3 {
    color: #28a745;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

#responseContent {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    max-height: 400px;
    overflow-y: auto;
}

.copy-btn {
    margin-top: 10px;
    padding: 8px 15px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.copy-btn:hover {
    background: #5a6268;
}

.error-message {
    background: #f8d7da;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #dc3545;
}

.error-message h3 {
    color: #dc3545;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

#errorContent {
    color: #721c24;
    font-weight: 500;
}

.examples-section {
    border-top: 2px solid #e9ecef;
    padding-top: 30px;
}

.examples-section h3 {
    margin-bottom: 20px;
    color: #495057;
    text-align: center;
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.example-btn {
    padding: 15px 20px;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    font-weight: 500;
    text-align: left;
}

.example-btn:hover {
    background: #e9ecef;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.8;
}

footer a {
    color: white;
    text-decoration: none;
    font-weight: 600;
}

footer a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    main {
        padding: 20px;
    }
    
    .input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    #submitBtn {
        margin-top: 10px;
    }
    
    .examples-grid {
        grid-template-columns: 1fr;
    }
}
