const express = require('express');
const cors = require('cors');
const axios = require('axios');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Moralis Cortex API endpoint
const CORTEX_API_URL = 'https://cortex-api.moralis.com/v1/cortex/chat';

// API proxy route
app.post('/api/cortex/chat', async (req, res) => {
    try {
        const { query, chatId } = req.body;

        if (!query) {
            return res.status(400).json({ error: 'Query is required' });
        }

        if (!process.env.MORALIS_API_KEY) {
            return res.status(500).json({ error: 'Moralis API key not configured' });
        }

        // Make request to Moralis Cortex API
        const response = await axios.post(CORTEX_API_URL, {
            query: query,
            ...(chatId && { chatId: chatId })
        }, {
            headers: {
                'X-API-Key': process.env.MORALIS_API_KEY,
                'Content-Type': 'application/json'
            },
            timeout: 30000 // 30 second timeout
        });

        res.json(response.data);

    } catch (error) {
        console.error('Cortex API Error:', error.response?.data || error.message);
        
        if (error.response) {
            // API returned an error response
            const status = error.response.status;
            const message = error.response.data?.message || error.response.data?.error || 'API request failed';
            
            if (status === 401) {
                res.status(401).json({ error: 'Invalid API key' });
            } else if (status === 429) {
                res.status(429).json({ error: 'Rate limit exceeded. Please try again later.' });
            } else if (status >= 500) {
                res.status(500).json({ error: 'Moralis service temporarily unavailable' });
            } else {
                res.status(status).json({ error: message });
            }
        } else if (error.code === 'ECONNABORTED') {
            res.status(408).json({ error: 'Request timeout. Please try again.' });
        } else {
            res.status(500).json({ error: 'Network error. Please check your connection.' });
        }
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        timestamp: new Date().toISOString(),
        apiKeyConfigured: !!process.env.MORALIS_API_KEY
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log(`API Key configured: ${!!process.env.MORALIS_API_KEY}`);
    
    if (!process.env.MORALIS_API_KEY) {
        console.warn('⚠️  WARNING: MORALIS_API_KEY not found in environment variables');
        console.warn('   Please create a .env file with your Moralis API key');
    }
});
