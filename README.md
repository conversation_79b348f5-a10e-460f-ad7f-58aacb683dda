# Moralis Cortex Simple Web Interface

A simple web interface to interact with the Moralis Cortex API using natural language queries.

## Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Get your Moralis API key:
   - Go to [Moralis Developer Portal](https://admin.moralis.com/api-keys)
   - Sign up/login and get your API key

3. Create `.env` file:
   ```bash
   cp .env.example .env
   ```
   
4. Add your API key to `.env`:
   ```
   MORALIS_API_KEY=your_actual_api_key_here
   ```

5. Start the server:
   ```bash
   npm start
   ```

6. Open your browser and go to: `http://localhost:3000`

## Usage

Simply type your blockchain-related questions in natural language, such as:
- "What's the current price of PEPE?"
- "Show me the NFTs owned by vitalik.eth"
- "What tokens does wallet 0xabc...123 hold?"

The AI will analyze your query and return relevant blockchain data.
